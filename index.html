<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover" />
    <title>Pomodoro Timer</title>
    <link rel="icon" type="image/png" href="/src/assets/pomodoro-icon.png" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Prevent FOUC by hiding content until CSS loads -->
    <style>
      /* Critical CSS to prevent flash of unstyled content */
      body {
        visibility: hidden;
        opacity: 0;
        transition: opacity 0.3s ease-in-out;
      }

      body.loaded {
        visibility: visible;
        opacity: 1;
      }

      /* Minimal styling for immediate render */
      #app {
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: 'Inter', system-ui, sans-serif;
      }
    </style>
  </head>
  <body>
    <div id="app">
      <!-- Now Playing Background -->
      <div id="now-playing" class="now-playing hidden">
        <div id="now-playing-content" class="now-playing-content">
          <span id="now-playing-text-1" class="now-playing-text">Now Playing - No music selected</span>
          <span id="now-playing-text-2" class="now-playing-text">Now Playing - No music selected</span>
        </div>
      </div>

      <!-- Main Timer Interface -->
      <div id="timer-container" class="container">
        <header class="header">
          <h1 class="app-title">Pomodoro Timer</h1>
          <div class="header-buttons">
            <button id="report-btn" class="report-btn">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                <path d="M3 13h2v8H3v-8zm4-6h2v14H7V7zm4-6h2v20h-2V1zm4 8h2v12h-2V9zm4-2h2v14h-2V7z"/>
              </svg>
            </button>
            <button id="settings-btn" class="settings-btn">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 15.5A3.5 3.5 0 0 1 8.5 12A3.5 3.5 0 0 1 12 8.5a3.5 3.5 0 0 1 3.5 3.5a3.5 3.5 0 0 1-3.5 3.5m7.43-2.53c.04-.32.07-.64.07-.97c0-.33-.03-.66-.07-1l2.11-1.63c.19-.15.24-.42.12-.64l-2-3.46c-.12-.22-.39-.31-.61-.22l-2.49 1c-.52-.39-1.06-.73-1.69-.98l-.37-2.65A.506.506 0 0 0 14 2h-4c-.25 0-.46.18-.5.42l-.37 2.65c-.63.25-1.17.59-1.69.98l-2.49-1c-.22-.09-.49 0-.61.22l-2 3.46c-.13.22-.07.49.12.64L4.57 11c-.04.34-.07.67-.07 1c0 .33.03.65.07.97l-2.11 1.66c-.19.15-.25.42-.12.64l2 3.46c.12.22.39.3.61.22l2.49-1.01c.52.4 1.06.74 1.69.99l.37 2.65c.04.24.25.42.5.42h4c.25 0 .46-.18.5-.42l.37-2.65c.63-.26 1.17-.59 1.69-.99l2.49 1.01c.22.08.49 0 .61-.22l2-3.46c.12-.22.07-.49-.12-.64l-2.11-1.66Z"/>
              </svg>
            </button>
          </div>
        </header>

        <main class="main-content">
          <!-- Timer Display -->
          <div id="timer-display" class="timer-display">
            <div class="session-info">
              <span id="session-type" class="session-type">Focus Time</span>
              <span id="session-counter" class="session-counter">Session 1</span>
            </div>

            <div id="timer-circle" class="timer-circle">
              <div class="timer-time">
                <span id="timer-minutes" class="timer-minutes">25</span>
                <span class="timer-separator">:</span>
                <span id="timer-seconds" class="timer-seconds">00</span>
              </div>
            </div>
          </div>

          <!-- Timer Controls -->
          <div class="timer-controls">
            <button id="start-pause-btn" class="control-btn primary">Start</button>
            <button id="reset-btn" class="control-btn secondary">Reset</button>
            <button id="skip-btn" class="control-btn secondary">Skip</button>
          </div>

          <!-- Stats -->
          <div class="stats">
            <div class="stat-item">
              <span class="stat-label">Completed Sessions</span>
              <span id="completed-sessions" class="stat-value">0</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Total Focus Time</span>
              <span id="total-focus-time" class="stat-value">0h 0m</span>
            </div>
          </div>


        </main>
      </div>

      <!-- Break Confirmation Screen -->
      <div id="break-confirmation" class="container hidden">
        <div class="break-content">
          <h2 id="break-title" class="break-title">Time for a Break!</h2>
          <p id="break-message" class="break-message">You've completed a focus session. Ready to start your break?</p>

          <div class="break-info">
            <div class="break-duration">
              <span id="break-type" class="break-type">Short Break</span>
              <span id="break-time" class="break-time">5 minutes</span>
            </div>
            <div id="bonus-time" class="bonus-time hidden">
              <span class="bonus-label">Bonus Time:</span>
              <span id="bonus-minutes" class="bonus-value">+2 minutes</span>
            </div>
          </div>

          <div class="break-controls">
            <button id="start-break-btn" class="control-btn primary large">Start Break</button>
            <button id="skip-break-btn" class="control-btn secondary">Skip Break</button>
          </div>
        </div>
      </div>

      <!-- Report Modal -->
      <div id="report-modal" class="modal hidden">
        <div class="modal-overlay"></div>
        <div class="modal-content">
          <div class="modal-header">
            <h2>Session Report</h2>
            <button id="close-report-btn" class="close-btn">×</button>
          </div>
          <div class="modal-body">
            <div class="report-stats">
              <div class="stat-item">
                <div class="stat-label">Total Focus Time</div>
                <div id="report-focus-time" class="stat-value">00:00:00</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">Completed Sessions</div>
                <div id="report-sessions" class="stat-value">0</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Settings Panel -->
      <div id="settings-panel" class="settings-panel hidden">
        <div class="settings-content">
          <header class="settings-header">
            <h2>Settings</h2>
            <button id="close-settings-btn" class="close-btn">×</button>
          </header>

          <div class="settings-sections">
            <!-- Timer Settings -->
            <section class="settings-section">
              <h3>Timer Settings</h3>
              <div class="setting-item">
                <label for="work-duration">Work Session (minutes)</label>
                <input type="number" id="work-duration" min="1" max="60" value="25">
              </div>
              <div class="setting-item">
                <label for="short-break-duration">Short Break (minutes)</label>
                <input type="number" id="short-break-duration" min="1" max="30" value="5">
              </div>
              <div class="setting-item">
                <label for="long-break-duration">Long Break (minutes)</label>
                <input type="number" id="long-break-duration" min="1" max="60" value="15">
              </div>
              <div class="setting-item">
                <button id="save-timer-settings" class="save-btn">Save Timer Settings</button>
              </div>
            </section>

            <!-- Music Settings -->
            <section class="settings-section">
              <h3>Music Settings</h3>
              <div class="setting-item horizontal">
                <label for="music-enabled">Enable Focus Music</label>
                <input type="checkbox" id="music-enabled" checked>
              </div>
              <div class="setting-item volume-control">
                <label for="music-volume">Volume</label>
                <input type="range" id="music-volume" min="0" max="100" value="50">
                <span id="volume-display">50%</span>
              </div>
              <div class="setting-item track-selection">
                <label for="music-track">Music Settings</label>
                <div style="margin-bottom: var(--spacing-sm);">
                  <button id="load-music-btn" class="control-btn secondary small" style="width: 100%;">Insert Music</button>
                </div>
                <div>
                  <label for="music-track" style="font-size: var(--font-size-sm); color: var(--text-secondary); margin-bottom: var(--spacing-xs); display: block;">Select Track:</label>
                  <select id="music-track" style="width: 100%;">
                    <option value="">Loading music tracks...</option>
                  </select>
                </div>
              </div>
            </section>

            <!-- Appearance Settings -->
            <section class="settings-section">
              <h3>Appearance</h3>
              <div class="setting-item">
                <label for="background-type">Background</label>
                <select id="background-type">
                  <option value="default">Default</option>
                  <option value="gradient">Custom Gradient</option>
                  <option value="solid">Solid Color</option>
                  <option value="image">Custom Image</option>
                </select>
              </div>
              <div class="setting-item" id="gradient-colors-setting">
                <label>Gradient Colors</label>
                <div class="gradient-color-controls">
                  <div class="color-input-group">
                    <label for="gradient-color-1">Color 1</label>
                    <input type="color" id="gradient-color-1" value="#667eea">
                  </div>
                  <div class="color-input-group">
                    <label for="gradient-color-2">Color 2</label>
                    <input type="color" id="gradient-color-2" value="#764ba2">
                  </div>
                </div>
              </div>
              <div class="setting-item hidden" id="solid-color-setting">
                <label for="background-color">Background Color</label>
                <input type="color" id="background-color" value="#667eea">
              </div>
              <div class="setting-item hidden" id="background-image-setting">
                <label for="background-image">Background Image</label>
                <input type="file" id="background-image" accept="image/*">
              </div>
            </section>
          </div>
        </div>
      </div>

      <!-- File Input for Music (Hidden) -->
      <input type="file" id="music-file-input" multiple accept="audio/mp3" style="display: none;">
    </div>

    <script type="module" src="/src/main.js"></script>
  </body>
</html>
